
        html,
        body {
            margin: 0px;
            padding: 0px;
            width: 100%;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #e4edf5 100%);
            min-height: 100vh;
            /* height: 100%; */
            /* overflow: hidden; */
        }

        #org-chart-organigrame-organisation_chart, 
        #org-chart-marine-organisation_chart {
            --org-color-title: #3c77ce;
            --org-color-name: #2e2e2e;
            --org-border-color: #75abff;
            --org-bg-color: #fafafa;
            background-color: transparent;
            width: 100%;
            /* height: 100%; */
            /* height: auto; */
            margin: 0 auto 60px;
        }
        #org-chart-organigrame-organisation_chart .link path,
        #org-chart-marine-organisation_chart .link path{
            stroke: var(--org-border-color);
        }
        #org-chart-organigrame-organisation_chart .boc-fill,
        #org-chart-marine-organisation_chart .boc-fill{
            stroke: var(--org-border-color);
            fill: var(--org-border-color);
        }

        #org-chart-organigrame-organisation_chart .boc-fill ~ line {
            stroke: #ffffff;
            stroke-width: 2;
        }

        #org-chart-organigrame-organisation_chart .node,
        #org-chart-marine-organisation_chart .node{
            background-color: #fafafa !important;
            border: 1px solid #ccc;
        }

        /* #org-chart-organigrame-organisation_chart .node rect {
            fill: #fafafa !important;
        } */

        /* #org-chart-organigrame-organisation_chart .organigram-title,
        #org-chart-marine-organisation_chart .organigram-title{
            font-size: 16px;
            color: var(--org-color-title);
            text-align: center;
            line-height: 20px;
            font-weight: 700;
        } */
         
        #org-chart-organigrame-organisation_chart .organigram-name,
        #org-chart-marine-organisation_chart .organigram-name{
            font-size: 16px;
            color: var(--org-color-name);
            text-align: center;
        }
        #org-chart-organigrame-organisation_chart .boc-search,
        #org-chart-marine-organisation_chart .boc-search{
            display: none;
        }

        /* Custom style card */
        #org-chart-organigrame-organisation_chart [data-n-id="99"] rect {
            fill: #042246;
        }
        /* #org-chart-organigrame-organisation_chart [data-n-id="99"] .organigram-title a {
           color: white !important;
        } */
        
        #org-chart-organigrame-organisation_chart [data-n-id="3"] rect,
        #org-chart-organigrame-organisation_chart [data-n-id="8"] rect {
            fill: #004ca5;
        }
        #org-chart-organigrame-organisation_chart [data-n-id="1"] .organigram-title,
        #org-chart-organigrame-organisation_chart [data-n-id="1"] .organigram-name,
        #org-chart-organigrame-organisation_chart [data-n-id="8"] .organigram-title,
        #org-chart-organigrame-organisation_chart [data-n-id="8"] .organigram-name,
        #org-chart-organigrame-organisation_chart [data-n-id="3"] .organigram-title,
        #org-chart-organigrame-organisation_chart [data-n-id="3"] .organigram-name {
            color: white;
        }

        #org-chart-organigrame-organisation_chart [data-n-id="110"] rect,
        #org-chart-organigrame-organisation_chart [data-n-id="111"] rect,
        #org-chart-organigrame-organisation_chart [data-n-id="113"] rect{
            fill: #e2edfa !important;
            stroke: #e2edfa !important;
        }
        #org-chart-organigrame-organisation_chart [data-n-id="2"] .organigram-name {
            color: #3c77ce;
        }
        #org-chart-organigrame-organisation_chart [data-l="3"] rect {
            fill: #fff;
            stroke: #fff;
        }

        /* partner firt level */
        #org-chart-organigrame-organisation_chart [data-n-id="98"] rect {
            fill: transparent;
        }

        /* Ahmed */
        #org-chart-organigrame-organisation_chart .organigram-name,
        #org-chart-organigrame-organisation_chart .organigram-title{
            font-size: 16px;
            text-align: center;
            line-height: 20px;
            font-weight: 700;
        }
        #org-chart-organigrame-organisation_chart [data-n-id="99"] .organigram-name,
        #org-chart-organigrame-organisation_chart [data-n-id="99"] .organigram-title{
            color: white;
        }
        #org-chart-organigrame-organisation_chart [data-n-id="107"] a,
        #org-chart-organigrame-organisation_chart [data-n-id="110"] a,
        #org-chart-organigrame-organisation_chart [data-n-id="111"] a,
        #org-chart-organigrame-organisation_chart [data-n-id="112"] a,
        #org-chart-organigrame-organisation_chart [data-n-id="113"] a,
        #org-chart-organigrame-organisation_chart [data-n-id="114"] a {
           color: #3C77CE;
        }

        /* 2eme org */
        .boc-light#org-chart-marine-organisation_char .organigram-name{
            font-size: 16px;
            color: var(--org-color-name);
            text-align: center;
        }
        .boc-light circle ~ line{
            stroke: #ffffff !important;
            stroke-width: 2 !important;
        }
        #org-chart-marine-organisation_chart .organigram-name,
        #org-chart-marine-organisation_chart a{
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        #org-chart-marine-organisation_chart [data-n-id="108"] .organigram-name,
        #org-chart-marine-organisation_chart [data-n-id="108"] a{
            color: white;
        }
        #org-chart-marine-organisation_chart [data-n-id="108"] rect {
            fill: #004ca5;
        }
        #org-chart-marine-organisation_chart [data-n-id="108"] rect {
            fill: #004ca5;
        }
        /* additional style */
        .container {
            max-width: 1024px;
            margin: 0 auto;
        }
        header {
            text-align: center;
            padding: 20px 0 40px;
        }
        
        h1 {
            color: #2c3e50;
            font-size: 2.5rem;
            margin-bottom: 10px;
            position: relative;
            display: inline-block;
        }
        
        h1:after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 4px;
            background: linear-gradient(90deg, #4a8efb 0%, #75abff 100%);
            border-radius: 2px;
        }