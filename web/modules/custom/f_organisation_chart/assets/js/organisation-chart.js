
(function (Drupal, drupalSettings) {
  'use strict';

  // Define the OrgChart template once globally
  if (typeof OrgChart !== 'undefined' && !OrgChart.templates.custom) {
    OrgChart.templates.custom = Object.assign({}, OrgChart.templates.ana);
    OrgChart.templates.custom.size = [280, 130];
    OrgChart.templates.custom.node = '<rect x="0" y="0" width="280" height="130" rx="16" fill="#ffffff" stroke="#75abff" stroke-width="1"/>';
    OrgChart.templates.custom.img_0 = '<foreignObject x="94" y="-30" width="70" height="70"><img style="display:block;margin:0 auto;border-radius:50%;width:70px;height:70px;box-shadow:0 2px 8px rgba(0,0,0,0.15);" src="{val}" /></foreignObject>';
    OrgChart.templates.custom.field_0 = '<foreignObject x="0" y="50" width="280" height="50"><div class="organigram-title" style="font-size:14px">{val}</div></foreignObject>';
    OrgChart.templates.custom.field_1 = '<foreignObject x="0" y="50" width="280" height="24"><div class="organigram-name">{val}</div></foreignObject>';
    // OrgChart.templates.custom.field_2 = '<foreignObject x="0" y="60" width="280" height="30"><div style="text-align:center;margin-top:4px;"><a href="{val}" target="_blank" style="">Voir le profil</a></div></foreignObject>';
  }

  /**
   * Initialize a single organisation chart instance
   */
  function initOrgChart(containerId, data) {
    var container = document.getElementById(containerId);
    if (!container || !data) {
      console.error('Organisation Chart: Container or data not found for', containerId);
      return null;
    }

    console.log('Initializing chart for container:', containerId, 'with data:', data);

    var chart = new OrgChart(container, {
      template: "custom",
      siblingSeparation: 20,      // space between siblings
      levelSeparation: 60,        // space between levels
      nodeBinding: {
        field_0: "desc",
        field_1: "name",
        img_0: "img",
        field_2: "link"
      },
      mouseScrool: OrgChart.action.scroll,
      scale: false,
      nodeMouseClick: OrgChart.action.expandCollapse,

      // disable zoom
      zoom: {
        speed: 0,
        smooth: 0
      },
      scaleMin: 1,
      scaleMax: 1,
      scaleInitial: 1,
      enableDragDrop: false,

      // disable search input and toolbar
      searchFields: [],
      searchUI: false,
      toolbar: false,

      scaleInitial: OrgChart.match.boundary,
      padding: 20,
      nodes: data
    });

    // Handle redraw events for this specific chart instance
    chart.on('redraw', function () {
      var svg = container.querySelector('svg');
      if (svg) {
        svg.setAttribute('viewBox', '-283 -20 1296 700');
      }
      // Only process elements within this specific container
      var titleDivs = container.querySelectorAll('.organigram-title');
      var nameDivs = container.querySelectorAll('.organigram-name');

      // Set all foreignObject in svg to org title height
      titleDivs.forEach(function (div) {
        var foreignObject = div.closest('foreignObject');
        var foreignObjectName = foreignObject.nextElementSibling;

        if (foreignObject) {
          // actual height
          var contentHeight = div.scrollHeight;
          // Update foreignObject height
          foreignObject.setAttribute('height', contentHeight + 10);
        }

        if (foreignObjectName) {
          foreignObjectName.setAttribute('y', parseInt(foreignObject.getAttribute('y')) + contentHeight + 10);
        }
      });
    });

    return chart;
  }

  /** 
   * Initialize all organisation charts on the page
   */
  function initAllCharts() {
    if (!drupalSettings.f_organisation_chart) {
      return;
    }

    // Initialize each chart based on drupalSettings data
    Object.keys(drupalSettings.f_organisation_chart).forEach(function(chartId) {
      var chartConfig = drupalSettings.f_organisation_chart[chartId];
      if (chartConfig && chartConfig.data) {
        initOrgChart(chartId, chartConfig.data);
      }
    });
  }

  // Initialize charts when DOM is ready
  Drupal.behaviors.organisationChart = {
    attach: function (context, settings) {
      // Only initialize once when the page is loaded or when new content is added
      if (typeof OrgChart !== 'undefined') {
        initAllCharts();
      } else {
        console.error('OrgChart library not loaded');
      }
    }
  };

})(Drupal, drupalSettings);
