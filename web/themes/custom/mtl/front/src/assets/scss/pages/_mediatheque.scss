.mediatheque {
    width: 100%;
    form#views-exposed-form-mediatheque-page-1 {
        margin-bottom: 30px;
        grid-template-columns: repeat(3, 1fr);
        input[checked="checked"] + label {
            color: $white;
        }
        .js-form-type-date {
            position: relative;
            // &:after {
            //     content: "\e901";
            //     font-family: 'icomoon';
            //     position: absolute;
            //     right: 17px;
            //     bottom: 14px;
            //     font-size: 18px;
            //     color: $primary;
            // } 
            // &:before {
            //     content: "";
            //     position: absolute;
            //     bottom: 5px;
            //     right: 4px;
            //     background: #ebf1fa;
            //     width: 45px;
            //     height: 45px;
            //     border-radius: 100%;
            // }
            label {
                all: unset;
            }
        }
        .form-radios {
            gap: 10px;
        }
    }
    .icon-play , .icon-img {
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        z-index: 1;
        background-color: $white;
        transition: background 450ms ease;
        width: 60px;
        height: 60px;
    }
    .icon-play {
        mask:url(../../assets/img/icons/icon-material-play-video.svg) no-repeat center / 100%;
    } 
    .icon-img {
        mask:url(../../assets/img/icons/icon-img-mediatheque.svg) no-repeat center / 100%;
    }
    .card {
        all: unset;
        .card-body {
            padding: 0;
        }
        p {
            font-family: $rubik-bold;
            font-size: 16px;
            margin-bottom: 0;
            html[dir="rtl"] & {
                font-family: "Cairo", sans-serif;
                font-weight: 700;
            }
        }
        .card-img-top {
            border-radius: 14px;
            transform: scale(1);
            min-height: 239px;
            transition: transform 450ms ease;
        }
        .picture {
            position: relative;
            min-height: 239px;
            border-radius: 14px;
            overflow: hidden;
            transition: transform 450ms ease;
            &:hover {
                .icon-play, .icon-img {
                    background: $primary;
                }
            }
            &:hover {
                .card-img-top {
                    transform: scale(1.01);
                }
            }
        }
    }
}