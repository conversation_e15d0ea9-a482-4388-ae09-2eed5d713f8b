.organismes {
    width: 100%;
    @media(max-width:767px) {
        .container{
          max-width: 100%;
        }
    }
    .swiper.organisme {
        padding-bottom: 80px;
        .swiper-slide {
            height: initial !important;
        }
        .swiper-pagination {
            bottom: 0 !important;
        }
        .slide-content {
            // position: relative;
            background: $white;
            border-radius: 10px;
            padding: 40px 0 0;
            box-shadow: 0 10px 12px rgba(67, 101, 151, .05);
            .picture {
                display: flex;
                justify-content: center;
                padding-bottom: 25px;
                img {
                    // height: 125px;
                    width: 100%;
                    object-fit: cover;
                    @media(max-width: 640px) {
                        height: 125px;
                    }
                }
            }
            .content {
                // padding-left: 25px;
                @include start(padding, 25px);
                padding-bottom: 50px;
                a {
                    position: relative;
                    z-index: 99;
                }
            }
            p {
                color: $black;
                padding: 10px 0 20px;
            }
            h3 {
                font-size: rem(20);
                color: $black;
            }
            
        }
        @media(max-width: 600px) {
            padding-bottom: 0;
        }
        &.marge-organisme {
            padding-bottom: 0 !important;
        }
        @include swiper-dots;
    }
    &.listing {
        header{
            p {
                font-family: $rubik-bold;
                margin-bottom: 20px !important;
                html[dir="rtl"] & {
                    font-family: "Cairo", sans-serif;
                    font-family: 700;
                }
            }
        }
        .card {
            height: 100%;
            .picture {
                margin: 0 auto;
                padding: 0;
                display: flex;
                align-items: center;
                img {
                    width: max-content;
                    // height: 150px;
                    // object-fit: cover;
                       max-height: 100%;
                    max-width: 100%;
                    object-fit: contain;
                    @media(max-width: 640px) {
                        height: 150px;
                    }
                }
            }
            .card-body {
                padding: 10px 25px;
                h3 {
                    font-size: 20px;
                }
                p {
                    padding: 10px 0 60px;
                }
                a {
                    position: absolute;
                    bottom: 20px;
                    padding: 10px 30px;
                }
            }
        }
    }
    &.partenaire {
        .card {
            overflow: hidden;
            padding: 0 20px;
             .picture {
                height: 150px;
                padding: 20px 0;
                img {
                    max-height: 100%;
                    max-width: 100%;
                    object-fit: contain;
                }
            }
             .card-body {
                h3 {
                    margin-top: 10px;
                    font-size: 18px;
                    a {
                        padding: 0 !important;
                    }
                    @media(max-width: 576px) {
                     height: auto !important;
                    }
                }
                a {
                    position: relative;
                    margin-top: 20px;
                }
             }
        }
        @media(max-width: 599px) {
            .container {
                padding-right: 15px;
            }
        }
    }
    @include container-slide;
}