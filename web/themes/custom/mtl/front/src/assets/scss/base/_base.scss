// -----------------------------------------------------------------------------
// This file contains very basic styles.
// -----------------------------------------------------------------------------

html {
  box-sizing: border-box;
}

/**
 * See: https://css-tricks.com/inheriting-box-sizing-probably-slightly-better-best-practice/
 */
*,
*::before,
*::after {
  box-sizing: inherit;
}

/**
 * Basic styles for links
 */
a {
  // color: $primary;
  text-decoration: none;
}
.click-me {
  position: absolute;
  inset: 0;
  z-index: 3;
}

//Marge Block

.margeBlock {
  margin: 48px 0;

  // @media (max-width: 640px) {
  //   margin: 30px 0;
  // }
}
// .margeSlider {
//   margin: 32px 0;
//   // @media (max-width: 640px) {
//   //   margin: 30px 0;
//   // }
// }
.mb-slider {
  margin: 32px 0 10px;
}
.margeBottom {
  margin-bottom: 60px;
}
.secteur-service {
  h2 {
    margin-bottom: 15px;
  }
}

//bgOverley

.bgOverley{
  position: fixed;
  top: 0;
  display: none;
  z-index: 1101;
  &:after{
      content: "";
      position: absolute;
      inset: 0;
      background-color: rgba($black, .6);
      width: 100vw;
      height: 100vh;
      inset: 0;
  }
  &.open{
      display: block;
  }
}

/****** GO to Top ******/

.goTop {
  position: fixed;
  bottom: rem(80);
  // right: rem(12);
  @include end(position, 20px);
  background: $primary;
  box-shadow: 0px 1px 10px 0px rgba(0, 0, 0, 0.15);
  border-radius: 50px;
  margin: 0;
  opacity: 0;
  transform: translateY(150%);
  z-index: 1030;
  transition: transform 1000ms ease-in-out, opacity 1000ms ease-in-out;
  width: rem(50);
  height: rem(50);
  cursor: pointer;
  box-shadow: rgba(0, 0, 0, 0.05) 0px 0px 0px 1px, rgb(209, 213, 219) 0px 0px 0px 1px inset;
  &:before {
    content: "\e903";
    position: absolute;
    font-family: 'icomoon';
    // font-size: 10px;
    zoom: .6;
    transform: translate(-50%, -50%) rotate(180deg);
    left: 50%;
    // @include start(position, 50%);
    top: 50%;
    text-align: center;
    z-index: 99;
    color: $white;
    transition: all 250ms ease-in-out;
    // animation: slideTop 650ms ease-in-out infinite;
  }
  &:hover {
      background: $secondary;
      // &:before {
      //   transform: translate(-50%, -50%) rotate(180deg);
      // }
  }
  &.is-visible {
    opacity: 1;
    transform: translateY(0);
    transition: transform 1000ms ease-in-out, opacity 1000ms ease-in-out;
    body.toggle & {
      transform: translateY(300%);
    }
  }
}

//Style Card

.card {
  border-width: 0;
  background-color: $white;
  box-shadow: 0 10px 12px rgba(67, 101, 151, .05);
  transition: box-shadow 450ms ease;
  border-radius: 14px;
  // cursor: pointer;
  img {
      border-top-left-radius: 14px;
      border-top-right-radius: 14px;
  }
  .card-body {
      padding: 25px 20px;
      h3 {
          font-family: $rubik-bold;
          font-weight: bold;
          font-size: rem(16);
          color: $black;
          line-height: 1.4;

          html[dir="rtl"] & {
            font-family: "Cairo", sans-serif;
          }

      }
      p {
          font-family: $quicksand-regular;
          margin-bottom: 8px;
          html[dir="rtl"] & {
            font-family: "Cairo", sans-serif;
            // font-weight: 400;
          }
      }
      span {
        font-family: $quicksand-regular;
        color: #6B6B6B;
        font-size: rem(14);
        html[dir="rtl"] & {
          font-family: "Cairo", sans-serif;
          font-weight: 400;
        }
      }
  }
  // &:hover {
  //   box-shadow: 0 10px 12px rgba(67, 101, 151, 0.27);
  // }
  .quotes {
    &::after {
      content: "";
      background: url($images-path + 'icons/quotes.svg') no-repeat 0 0;
      position: absolute;
      width: 139px;
      height: 111px;
      top: -30px;
      right: 180px;
    }
  }
  @media(max-width: 768px) {
    .quotes {
      &::after {
        background: url($images-path + 'icons/quotes-mobile.svg') no-repeat 0 0;
        width: 59px;
        height: 47px;
        top: -17px;
        right: 65px;
      }
    }
  }
}

//breadcrumb
.breadCrumb {
  position: absolute;
  bottom: 40px;
  width: 100%;
  background: transparent;
  margin-bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  @media(max-width: 600px) {
    bottom: 30px;
  }
  ul {
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0;
      padding: 0;
      flex-wrap: wrap;
      gap: 10px;
      li {
          position: relative;
          font-family: $quicksand-bold;
          font-size: 15px;
          line-height: 1;
          color: $white;  
          letter-spacing: .3px;
          transition: all 450ms ease-in-out;
          text-shadow: 3px 3px 4px #000;
           html[dir="rtl"] & {
              font-family: "Cairo", sans-serif;
              font-weight: bold;
            }
          a {
            font-family: $quicksand-regular;
            color: $white;
            font-size: 15px;
            transition: all 450ms ease-in-out;
            html[dir="rtl"] & {
              font-family: "Cairo", sans-serif;
              font-weight: 400;
            }
          }
          &:not(:last-child) {
            font-family: $quicksand-regular;
            // padding-right: 20px;
            @include end(padding, 20px);
            html[dir="rtl"] & {
              font-family: "Cairo", sans-serif;
              font-weight: 400;
            }
          }
          &:hover {
              a {
                  color: $secondary;
              }
          }
          .fa-slash {
            position: absolute;
            transform: rotate(75deg);
            font-size: 11px;
            top: 2px;
            @include end(position, 0px);
          }
          @media(max-width: 1024px) {
            &:nth-last-child(2) {
              i {
                display: none;
              }
            }
            &:last-child {
              display: none;
            }
          }
          @media(max-width: 480px) {
            font-size: 14px;
            a {
              font-size: 14px;
            }
          }
      }
  }
}

// Tabs BO
.breadCrumb + div ~ ul, .breadCrumb  ~ ul {
  display: flex;
  margin: 0;
  gap: 20px;
  justify-content: center;
  top: 20px;
  position: relative;
  li {
    a {
      color: $white;
      background: $primary;
      border-radius: 14px;
      padding: 8px 20px;
      transition: all 450ms ease;
      &:hover {
        background: $secondary;
      }
    }
  }
}

//Form Back office
.user-login-form, .user-pass, .user-register-form {
  width: 90%;
  max-width: 500px;
  margin: 80px auto 50px;
  background: $white;
  box-shadow: 0 10px 12px rgba(67, 101, 151, .05);
  border-radius: rem(15);
  padding: 20px;
  font-size: 12px;
  .form-item {
    margin-bottom: 20px;
  }
  label {
    font-family: $quicksand-bold;
    font-size: rem(14);
    html[dir="rtl"] & {
      font-family: "Cairo", sans-serif;
      font-weight: 700;
    }
  }
  input[type="text"], input[type="password"] {
    font-family: $quicksand-regular;
    background-color: $white;
    width: 100%;
    border: 1px solid $black;
    border-radius: rem(30);
    color: $black;
    font-size: rem(16);
    padding: 15px 20px;
    outline: none;
    html[dir="rtl"] & {
      font-family: "Cairo", sans-serif;
      font-weight: 400;
    }
  }
  input[type="submit"] {
    font-family: $quicksand-bold;
    border: 1px solid $secondary;
    background: transparent;
    color: $secondary;
    border-radius: 24px;
    padding: 14px 22px;
    width: 50%;
    font-size: 16px;
    margin: 0 auto;
    display: flex;
    transition: all 450ms ease;
     html[dir="rtl"] & {
      font-family: "Cairo", sans-serif;
      font-weight: 700;
    }
    &:hover {
      background: $secondary;
      color: $white;
    }
  }
  p {
    font-family: $quicksand-bold;
    margin-bottom: 20px;
     html[dir="rtl"] & {
      font-family: "Cairo", sans-serif;
      font-weight: 700;
    }

  }
  .description {
    padding: 5px 0;
    font-family: $quicksand-regular;
    html[dir="rtl"] & {
      font-family: "Cairo", sans-serif;
      font-weight: 400;
    }
  }
  input[type="email"], input.username  {
    margin-bottom: 5px;
  }
  summary[role="button"] {
    font-family: $quicksand-regular;
    margin-bottom: 15px;
    font-size: 16px;
    html[dir="rtl"] & {
      font-family: "Cairo", sans-serif;
      font-weight: 400;
    }
  }
  input.button--primary {
    white-space: normal;
    width: 60%;
  }
}

//Form Style Globval
input[type="text"], input[type="password"], input[type="date"], input[type="email"], textarea, .selectric {
  font-family: $quicksand-regular;
  background-color: $white;
  width: 100%;
  border: 1px solid rgba($black, .5);
  border-radius: rem(30);
  color: rgba($black, .5);
  font-size: rem(15);
  padding: 15px 20px;
  outline: none;
  height: 50px;
  &::placeholder {
    color: rgba($black, .5);
  }
  html[dir="rtl"] & {
    font-family: "Cairo", sans-serif;
    font-weight: 400;
  }
}
input[type="submit"] {
  font-family: $quicksand-bold;
  border: 1px solid $secondary;
  background: transparent;
  color: $secondary;
  border-radius: 24px;
  padding: 14px 22px;
  width: 50%;
  font-size: 16px;
  margin: 0 auto;
  display: flex;
  transition: all 450ms ease;
   html[dir="rtl"] & {
      font-family: "Cairo", sans-serif;
      font-weight: 700;
    }
  &:hover {
    background: $secondary;
    color: $white;
  }
}
form {
  background: $white;
  box-shadow: 0 10px 12px rgba(67, 101, 151, .05);
  border-radius: 14px;
  padding: 30px;
  .form-item {
    & > label {
      padding-bottom: 8px !important;
      display: block !important;
      text-transform: lowercase !important;
      &::first-letter {
        text-transform: uppercase !important;
      }
    }
  }
  .form--inline {
    display: flex;
    gap: 10px;
  }
  .form--inline .form-item {
    margin-right: 0;
  }
  .fieldgroup {
    &:first-of-type {
      margin-bottom: rem(15);
    }
    
  }
  .form-radios.form--inline, .form-radios {
    display: flex;
    // gap: 10px;
    flex-wrap: wrap;
  }
  
  input[type="radio"],
  input[type="checkbox"]
  {
    display: none;
  }
  // input[checked] + label {
  //   background: $primary !important;
  //   color: $white !important;
  // }
  span {
    font-family: $quicksand-bold;
    font-size: rem(15);
    color: $black;
    text-transform: capitalize;
     html[dir="rtl"] & {
      font-family: "Cairo", sans-serif;
      font-weight: 700;
    }
  }
  @media(max-width: 1024px) {
    padding: 30px;
  }
  @media(max-width: 600px) {
    padding: 30px;
    .form-radios.form--inline {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      margin-bottom: 60px;
      .form-item {
        &:first-of-type {
          grid-column: 1 / -1;
          width: calc(50% - 20px);
        }
        &:nth-of-type(2) {
          grid-column: 1 / -1;
          width: 70%;
        }
      }
    }
  }
  @media(max-width: 480px) {
    padding: 20px 18px;
  }
  label[for^="edit-field-secteur-target-id-43"] {
    display: none !important;
  }
  label[for^="edit-tid-43"] {
    display: none !important;
  }
  input[checked] + label[for="edit-contact--2"] {
    background: none !important;
    color: $black !important;
  }

}
input.form-text {
  margin-top: 0 !important;
}
//Block E-SERVICE FORM
#views-exposed-form-e-services-block-1 {
  #edit-field-profil-target-id--wrapper {
      legend {
        display: none;
      }
  }
  div[id^="edit-field-profil-target-id"] {
    & > div:first-child {
      display: none !important;
    }
    background: rgba($black, .04);
    display: flex;
    margin-left: auto;
    margin-right: auto;
    width: max-content;
    outline-offset: 4px;
    border-radius: 27px;
    gap: 20px;
    padding: 4px;
    & > div {
      label {
        font-family: $rubik-bold;
        font-weight: bold;
        border-radius: 24px;
        padding: 6px 20px;
        display: flex;
        align-items: center;
        text-transform: uppercase;
        color: $border;
        font-size: rem(15);
        cursor: pointer;
        background: transparent;
        html[dir="rtl"] & {
          font-family: "Cairo", sans-serif;
        }


      }
      input {
        display: none;
      }
      input:checked ~ label {
        background: $white !important;
        color: $black !important;
      }
    }
    @media(max-width:480px) {
      gap: 5px;
      width: 100%;
      & > div {
        width: 49%;
        flex: 0 0 49%;
        label {
          padding: 6px 0;
          justify-content: center;
          font-size: clamp(12px, 3.5vw, 15px);
        }
      }
    }
  }
}
form#views-exposed-form-e-services-page-1 {
  div[id^="edit-field-profil-target-id"] {
    .form-radios.form--inline {
      .form-item {
        @media(max-width: 600px) {
            // width: 100%;
          //  grid-column: 1 / 2;
          &:nth-of-type(2) {
            grid-column: 1 / 2;
            width: 100%;
          }
        }
      }
    }
  }
  
}


//Formes Specifique
form#views-exposed-form-actualite-page-1,
form#views-exposed-form-mediatheque-page-1,
form#views-exposed-form-projects-page-1,
form#views-exposed-form-appel-offre-page-1,
form#views-exposed-form-carrieres-page-1,
form#views-exposed-form-communiques-de-presse-page-1,
form#views-exposed-form-reglementation-page-1,
form#views-exposed-form-agenda-page-1,
form#views-exposed-form-publication-page-1,
form#views-exposed-form-e-services-page-1,
form#views-exposed-form-search-page-1,
form#views-exposed-form-faq-page-1 {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 6px;
  label {
    font-size: rem(16);
    padding: 7px 18px;
    border-color: $primary;
    border-width: 1px;
    border-style: solid;
    border-radius: 24px;
    transition: all 450ms ease-in;
    color: $primary;
    cursor: pointer;
    display: flex;
    justify-content: center;
    &:hover {
        background-color: $primary;
        color: $white;
    }
  }
  label[for^="edit-title"], 
  label[for^="edit-combine"], 
  label[for^="edit-search-api-fulltext"],
  label[for^="edit-type"]
   {
    all: unset;
    font-family: $quicksand-bold;
    font-size: rem(15);
    color: $black;
    text-transform: capitalize;
     html[dir="rtl"] & {
      font-family: "Cairo", sans-serif;
      font-weight: 700;
    }
    &:hover {
      background: transparent;
    }
  }
  .fieldgroup {
    grid-row: 2;
    grid-column: 1/-1;
    &:last-of-type {
      grid-row: 3;
      // display: flex;
      div[id^="edit-field-type-loi-target-id"] {
        display: flex;
        gap: 15px;
      }
    }
  }
  .js-form-item-combine {
    margin-bottom: 0.9375rem;
  }
  .input-wrapper {
    position: relative;
    height: 54px;
    width: rem(250);
    display: flex;
    align-self: end;
    margin-bottom: 0.9375rem;
    &:after {
      content: "";
      position: absolute;
      width: 16px;
      height: 16px;
      background: url($images-path + 'icons/icon-search.svg') no-repeat 0 0;
      // right: 60px;
      @include end(position, 45px);
      top: 50%;
      transform: translateY(-50%);
    }
    input[type="submit"] {
      all: unset;
      font-family: $rubik-bold;
      font-weight: bold;
      font-size: rem(18);
      text-transform: uppercase;
      height: 100%;
      border-radius: 24px;
      transition: all 450ms ease-in;
      color: $white;
      cursor: pointer;
      width: 100%;
      background: $secondary;
      // padding-left: rem(40);
      @include start(padding, 50px);
      transition: all 400ms ease;
      height: 50px;
      align-self: flex-end;
      html[dir="rtl"] & {
        font-family: "Cairo", sans-serif;
      }

    }
    &:hover {
      input[type="submit"] {
        background: $primary;
      }
    }
  }
  .highlight, .highlight * {
    text-decoration: none !important;
    color: $white $white;
    background-color: $primary !important;
    border-radius: 24px;
  }
  
  input:checked ~ label {
    background: $primary;
    color: $white;
  }
  @media(max-width: 992px) {
    grid-template-columns: repeat(2, 1fr);
  }
  @media(max-width: 600px) {
    label {
      font-size: 13px;
      padding: 10px 5px;
    }
    & > .form-item {
      grid-column: 1 / -1;
    }
    .input-wrapper {
      grid-column: 1 / -1;
      grid-row: 3;
      width: 100%;
    }
  }
  @media(max-width: 376px) {
    label {
      font-size: 12px;
    }
  }
  
}
form#views-exposed-form-mediatheque-page-1,
form#views-exposed-form-appel-offre-page-1,
form#views-exposed-form-carrieres-page-1 {
  grid-template-columns: 300px 230px 250px;
  input {
    font-family: $quicksand-regular;
    font-size: 16px;
    html[dir="rtl"] & {
      font-family: "Cairo", sans-serif;
      font-weight: 400;
    }
  }
  input[type="text"] {
    color: $black;
    &::placeholder {
      color: $black;
    }
  }
  @media(max-width:992px) {
    grid-template-columns: repeat(2, 1fr);
  }
}

//Delete input[type="submit"]
form#views-exposed-form-publication-page-1,
form#views-exposed-form-e-services-page-1 {
  @media(max-width: 600px) {
    .form-radios.form--inline {
      margin-bottom: 0;
    }
  }
  .input-wrapper {
    display: none;
  }
  input[type="submit"] {
    display: none;
  }
}

//Supp la parge de form--inline
form#views-exposed-form-reglementation-page-1 {
  @media(max-width: 600px) {
    padding: 30px;
    div[id^="edit-field-secteur-target-id"] {
      .form-radios.form--inline {
          margin-bottom: 0;
      }
    } 
    div[id^="edit-field-type-loi-target-id"] {
      .form-radios.form--inline {
        margin-bottom: 80px;
        width: 100%;
      }
    }
  }
}

//Mediatheque Appelle Offre carrieres pro
form#views-exposed-form-mediatheque-page-1,
form#views-exposed-form-appel-offre-page-1,
form#views-exposed-form-carrieres-page-1 {
  @media(max-width: 600px) {
    gap: 20px;
    fieldset[id^="edit-field-media-type--wrapper"],
    fieldset[id^="edit-field-type--wrapper"] {
      grid-row: 4 !important;
      label {
        padding: 10px 15px;
      }
    }
    .js-form-type-date {
      &::before {
        bottom: 3px !important;
      }
    }
  }
  @media(max-width: 480px) {
    fieldset[id^="edit-field-media-type--wrapper"] {
      label {
        padding: 10px 10px;
        font-size: 12px;
      }
    }
  }
  
}
//Form selectric
.selectric-wrapper {
  &.selectric-open {
    .selectric {
      .button {
        &:after {
          transform: translate(-50%, -50%) rotatez(180deg);
        }
      }
    }
  }
}
.selectric {
  // width: 100%;
  // padding: 0;
  padding: 10px 20px;
  outline: none;
  height: 54px;
  .button {
    background: transparent;
    transform: translateY(-50%);
    top: 50%;
    right: 20px;
    &:after {
      content: "\e903";
      position: absolute;
      font-family: 'icomoon';
      font-size: 8px;
      color: $black;
      right: auto;
      border: none;
      width: auto;
      margin: initial;
      transform: translate(-50%, -50%) rotatez(0deg);
      top: 50%;
      left: 50%;
      transition: transform 450ms ease-in-out;
    }
  }
  .label {
    font-family: $quicksand-regular;
    color: $black;
    font-size: 16px;
    text-transform: none;
    html[dir="rtl"] & {
      font-family: "Cairo", sans-serif;
      font-weight: 400;
    }
  }
}
.selectric-items {
  background: $white;
  border-color: $primary;
  border-radius: 0 0 15px 15px;
  box-shadow: 0 10px 12px rgba(67, 101, 151, .05);
  li {
    font-family: $quicksand-regular;
    font-size: rem(16);
    html[dir="rtl"] & {
      font-family: "Cairo", sans-serif;
      font-weight: 400;
    }
    // bor
    &.highlighted {
      background: $primary;
      color: $white;
    }
    &:last-child {
      border-radius: 15px;
    }
    &:hover {
      background: $primary;
      color: $white;
    }
  }
}

//zindex menu de BO
#toolbar-administration, #toolbar-administration *{
  z-index: 1031;
}
.contextual-region {
  position: inherit;
}

//Bloc Agenda Global
.bloc-agenda {
  .card {
      padding: 30px 30px 20px 40px;
      gap: 0 18px;
      display: grid;
      grid-template-columns: minmax(67px, 67px) 1fr;
      p {
          font-family: $rubik-bold;
          font-weight: bold;
          font-size: rem(18);
          width: 100%;
          html[dir="rtl"] & {
            font-family: "Cairo", sans-serif;
          }
      }
      p.para {
        grid-row: 2;
        grid-column: 2/-1;
        font-size: rem(19);
      }
      p:empty,
      p:has(*:empty) {
        display: none;
      }
      // @media (max-width:768px) {
      //     padding: 30px 30px 30px 20px;
      //     .card-body {
      //         container-type: inline-size;
      //     }
      //     p {
      //         font-size: rem(16);
      //     }
      // }
  }
  &.p-agenda {
    .card {
      img {
        width: 67px;
        height: 76px;
      }
    }
  }
}

//Pagination
nav.pager, nav[aria-labelledby="pagination-heading"] {
  display: flex;
  justify-content: center;
  margin-top: rem(40);
  ul{
    display: flex;
    flex-wrap: wrap;
    gap: 10px 0;
    margin-bottom: 0;
      .pager__item{
          &.pager__item--ellipsis {
            padding-top: 3px;
            align-self: center;
          }
          a{
              font-family: $rubik-bold;
              font-weight: bold;
              font-size: rem(20);
              width: rem(48);
              height: rem(48);
              display: flex;
              justify-content: center;
              align-items: center;
              color: $black;
              border-width: 1px;
              border-style: solid;
              border-color: $black;
              border-radius: 100%;
              transition: all .3s ease-in-out;
              &:hover {
                color: rgba(#000, .3);
                border-color: rgba(#000, .3);
              }
              @media(max-width:480px) {
                font-size: rem(18);
                width: rem(42);
                height: rem(42);
              }
              html[dir="rtl"] & {
                font-family: "Cairo", sans-serif;
              }

          }
          &:not(:last-child){
              // margin-right: rem(13);
              @include end(margin, rem(13));
              @media(max-width:480px) {
                @include end(margin, rem(8));
              }
          }
          &.pager__item--next, &.pager__item--previous, &.pager__item--first, &.pager__item--last {
              a {
                  color: rgba(#000, .3);
                  border-color: rgba(#000, .3);
                  i.fas {
                      display: none;
                  }
                  &:before {
                      content: "\e903";
                      font-family: 'icomoon';
                      position: absolute;
                      display: flex;
                      font-size: 8px;
                      transform: rotate(-90deg);
                      // @include transform-end(-90deg)
                      

                  }
                  span {
                    display: none;
                  }
                  html[dir="rtl"] & {
                    transform: rotate(180deg);
                  }
              }
          }
          &.pager__item--previous, &.pager__item--first {
              a {
                  &:before {
                      transform: rotate(90deg);
                  }
              }

          }
          &.pager__item--last, &.pager__item--first {
            a {
              position: relative;
              &:before {
                display: none;
              }
              &:after {
                  content: "";
                  mask: url($images-path + 'icons/arrow-double.svg') no-repeat 0 0;
                  width: 24px;
                  height: 24px;
                  background: rgba(#000, .3);
                  position: absolute;
                  left: 50%;
                  // top: 50%;
                  transform: translateX(-50%);

              }
              span {
                display: none;
              }
            }
          }
          &.pager__item--first {
            a {
              &:after {
                transform: translateX(-50%) rotate(180deg);
              }
            }
          }
          // &.pager__item--last, &.pager__item--first {
          //     display: none;
          // }
          &.is-active{
              a {
                width: rem(48);
                height: rem(48);
                display: flex;
                justify-content: center;
                align-items: center;
                transition: all .3s ease-in-out;
                // color: $black;
                border-width: 1px;
                border-style: solid;
                // border-color: $black;
                border-radius: 100%;
                color: $white;
                border-color: $primary;
                background-color: $primary;
                @media(max-width:480px) {
                  width: rem(42);
                  height: rem(42);
                }
              }
              
          }
          &:hover {
            a {
              color: $white;
              border-color: $primary;
              background-color: $primary;
              &:after {
                background: $white;
              }
            }
          }
          
      }
  }
}

//Style calendar
.calendar {
  width: 67px;
  height: 70px;
  border: 4px solid #17BBCE;
  border-radius: 10px;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  &:before {
    content: "";
    position: absolute;
    top: 12px;
    width: 100%;
    left: 0;
    height: 5px;
    background-color: #17BBCE;
  }
  span.tree {
    display: block;
    background: #17BBCE;
    height: 15px;
    width: 4px;
    border-radius: rem(50);
    position: relative;
    top: -16px;
    left: -10px;
    &::before,
    &::after {
        content: "";
        position: absolute;
        background: #17BBCE;
        height: 15px;
        border-radius: rem(50);
    }
    &::before {
      width: 4px;
      transform: translateX(24px);
      right: 0;
    }

    &::after {
        width: 4px;
        transform: translateX(12px);
    }
  }
  span {
    font-family: $quicksand-bold;
    font-style: normal;
    line-height: 1;
    color: #17BBCE;
    text-transform: uppercase;
     html[dir="rtl"] & {
      font-family: "Cairo", sans-serif;
      font-weight: 700;
    }
  }
}



/* ajax load  */
.ajax-progress-fullscreen {
  position: fixed;
  // left: 0;
  @include start(position, 0);
  top: 0;
  height: 100%;
  width: 100%;
  background-color: rgba(25,25,25,0.5);
  z-index: 9999;
  background-image: none;
}
.ajax-progress-fullscreen:before {
  width: 70px;
  height: 70px;
  border: 2px solid rgba(#fff, 0.4);
  border-top-color: #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  position: absolute;
  content: "";
  left: 50%;
  top: 50%;
  transform: translate(-50%,-50%);
}
@keyframes spin {
  0% { transform: rotate(0deg); }
  50% { transform: rotate(180deg); }
  100% { transform: rotate(360deg); }
}
//animation
// .card{
//   overflow: hidden;
//   .picture {
//     overflow: hidden;

//   }

//   img {
//     overflow: hidden;
//     transition: opacity 5s cubic-bezier(.19,1,.22,1) 0ms,transform 2s cubic-bezier(.215,.61,.355,1) 0ms;
//     transform: scale(1.05);
//     will-change: transform,opacity;
//   }
// }
// .picture{
//   overflow: hidden;
//   .animate-img {
//     overflow: hidden;
//     transition: opacity 5s cubic-bezier(.19,1,.22,1) 0ms,transform 2s cubic-bezier(.215,.61,.355,1) 0ms;
//     transform: scale(1.05);
//     will-change: transform,opacity;
//   }
// }

// #reveal1 {
//   opacity: 0;
//   transform: scale(0.8);
//   transition: all 1s ease-in-out;
// }
// #reveal1.visible {
//   opacity: 1;
//   transform: none;
// }
// .digit {
//   opacity: 0;
//   transform: translateY(150px);
//   transition: all 600ms ease-out;
// }
// .digit.visible {
//   opacity: 1;
//   transform: none;
// }

// .btn {
//   i {
//     animation: slideRight 650ms ease-in-out infinite;
//   }
//   &.download {
//     i {
//       animation: none;
//     }
//   }
// }
// @keyframes slideRight {
//   0% {
//     transform: translateX(0);
//   }
//   50% {
//     transform: translateX(5px);
//   }
//   100% {
//     transform: translateX(0);
//   }
// }

//
// .bloc-odd {
//   display: flex;
// }
.bloc-odd {
  gap: 30px;
  display: flex;
  &.is-reversed {
    flex-direction: row-reverse;
  }
  // .has-tabs-button &{
  //   display: block;
  // }
  @media(max-width: 767px) {
    flex-direction: column;
    &.is-reversed {
      flex-direction: column-reverse;
    }
  }
}
.presentation-left {
  width: 49%;
  float: left;
  justify-content: center;
  .card {
    box-shadow: none;
  }
  .card-body {
    // padding: 25px 30px 25px 40px !important;
    padding: 0;
  }
  img {
    height: 100%;
    object-fit: cover;
    border-radius: 14px !important;
    padding: 0 !important;
  }
  ul {
    margin-bottom: 0;
  }
  @media(max-width: 767px) {
    width: 100%;
  }
  ul + p {
    margin: 20px 0 0;
  }
  p {
    a {
      font-family: $quicksand-bold;
      text-decoration: none !important;
       html[dir="rtl"] & {
      font-family: "Cairo", sans-serif;
      font-weight: 700;
    }
      &:hover {
        color: $secondary !important;
      }
    }
  }
}
.presentation-right {
  width: 49%;
  float: right;
  justify-content: center;
  img {
    height: 100%;
    object-fit: cover;
    border-radius: 14px !important;
    padding: 0 !important;
  }
  ul {
    margin-bottom: 0;
  }
  .card {
    box-shadow: none;
  }
  .card-body {
    padding: 25px 30px 25px 40px !important;
  }
  @media(max-width: 767px) {
    width: 100%;
  }
}
.wysi-quotes {
  padding-top: 30px;
}
.clearfix {
  clear: both;
}
// @keyframes slideRight {
//   0% {
//     transform: translateX(0) rotate(0deg);
//   }
//   50% {
//     transform: translateX(5px) rotate(0deg);
//   }
//   100% {
//     transform: translateX(0) rotate(0deg);
//   }
// }

@keyframes slideLeft {
  0% {
    transform: translateX(0) rotate(180deg);
  }
  50% {
    transform: translateX(-5px) rotate(180deg);
  }
  100% {
    transform: translateX(0) rotate(180deg);
  }
}
@keyframes slideTop {
  0% {
    transform:  translate(-50%, -50%) rotate(180deg);
  }
  50% {
    transform:  translate(-50%, -80%) rotate(180deg);
  }
  100% {
    transform:  translate(-50%, -50%) rotate(180deg);
  }
}


//Style Valideion jQuery
label.error {
  font-family: $quicksand-regular;
  color: red;
  font-size: rem(14);
  width: 100%;
  html[dir="rtl"] & {
    font-family: "Cairo", sans-serif;
    font-weight: 400;
  }
}
.header {
  label.error {
    position: absolute;
    bottom: -25px;
    left: 0;
  }
}


//map
#tooltip {
  position: absolute;
  display: none;
  background: #17BBCE;
  color: #fff;
  padding: 5px;
  border-radius: 5px;
  pointer-events: none;
  left: 1176px; top: 1148px;
  z-index: 99;
}
.st0 {
  fill: #fff;
  stroke: #3C77CE;
  stroke-width: 1;
}
div#carteContainer path.active {
  fill: #3C77CE;
}
div#carteContainer path:hover {
  fill: #17BBCE;
}
div#carteContainer path {
  cursor: pointer;
}
// .region-maps .carte-item {
//   display: none;
// }
.region-maps .carte-item.active {
  display: block;
}



/* simplebar */

.simplebar {
  max-height: 350px;
}
.simplebar-track.simplebar-vertical {
  border-radius: 7px;
  background: #d0dfe6;
  top: 30px;
}
.simplebar-scrollbar:before {
  background: #000F26;
  opacity: 1;
}
.simplebar-scrollbar:before{
  opacity: 1;
  background: #000F26;
  top: 1px;
  left: 0px;
  right: 0px;
}
.simplebar-scrollbar.simplebar-visible:before {
  opacity: 1;
  border-radius: 7px;
}
.simplebar-hover.simplebar-visible {
  background: #000F26;
  height: 140px;
}

//Chifres cles
.group-chiffreCles {
  ul {
      display: flex;
      justify-content: space-between;
      background-color: $white;
      border-radius: 15px;
      padding: 25px 35px;
      place-items: center;
      gap: 20px;
      margin-bottom: 0;
      span {
          font-family: $rubik-bold;
          font-weight: bold;
          font-size: 22px;
          html[dir="rtl"] & {
            font-family: "Cairo", sans-serif;
          }

      }
      
  }
  &.region {
    ul {
      padding: 25px 50px;
      span {
          font-family: $quicksand-regular;
          font-size: 14px;
          html[dir="rtl"] & {
            font-family: "Cairo", sans-serif;
            font-weight: 400;
          }
      }
      p {
        font-family: $rubik-bold;
        font-size: 18px;
        html[dir="rtl"] & {
          font-family: "Cairo", sans-serif;
          font-weight: 700;
        }
      }
      li {
        flex: 1;
      }
    }
  }
  @media(max-width:600px) {
    ul {
      flex-direction: column;
      align-items: flex-start;
      padding: 20px !important;
    }
  }
}



/* Augmente la taille des images dans Fancybox */
.fancybox__slide.has-image > .fancybox__content {
  width: 40vw !important;
  height: 60vh !important;
}
.fancybox-image {
  border-radius: 14px;
  object-fit: cover;
}

.paragraph + .addtoany_list {
  display: none;
}

//Page de contenu 
//accordion accord-wysiwyg
.page-content {
  h2,h3,h4 {
    font-family: $rubik-bold;
    color: $primary;
    transform: none;
    html[dir="rtl"] & {
      font-family: "Cairo", sans-serif;
      font-weight: 700;
    }
  }
  h2 {
    font-size: 24px;
  }
  h3 {
    font-size: 22px;
  }
  h4 {
    font-size: 20px;
  }
}
.accord-wysiw, .faq .views-row{
  margin-bottom: 10px;
  h3, .views-field-title  {
    position: relative;
    display: block;
    font-size: rem(16);
    padding: 10px 30px;
    color: $black;
    margin-bottom: 0;
    cursor: pointer;
    &:after {
      content: "+";
      font-size: 25px;
      color: black;
      width: 42px;
      height: 42px;
      border: 1px solid black;
      border-radius: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      position: absolute;
      // right: 20px;
      @include end(position, 20px);
      top: 50%;
      transform: translateY(-50%);
    }
    &.active {
      color: $primary;
      a {
        color: $primary;
      }
      &:after {
        content: "-";
        color: $primary;
        border-color: $primary;
      }
    }
    @media(max-width: 600px) {
      &:after {
        font-size: 20px;
        width: 30px;
        height: 30px;
      }
    }
  }
  & > div, .views-field-body {
    display: none;
    padding: 10px 30px 30px 30px;
    p {
      span {
        color: $primary;
      }
    }
  }
}

//Bloc wysi
.bloc-wysi {
  img {
    width: 710px;
    border-radius: 14px;
    display: flex;
    margin: 0 auto;
  }
  p {
    span {
      color: $primary;
    }
  }
}
.card-btn {
  .card-body {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 30px;
    padding: 0;
   
    .btn {
      flex: 0 0 220px;
    }
    p {
      font-family: $rubik-bold !important;
      html[dir="rtl"] & {
        font-family: "Cairo", sans-serif !important;
        font-weight: 700 !important;
      }
    }
    @media(max-width:600px) {
      flex-direction: column;
      .btn {
        width: 220px;
        flex: 0;
      }
    }
  }

}
// .contextual .trigger {
//   display: none;
// }
.wrapper-accessibilty {
  z-index: 999;
}

.f6dof81 {
  box-shadow: rgba(0, 0, 0, 0.05) 0px 0px 0px 1px, #d1d5db 0px 0px 0px 1px inset !important;
}

form#node-preview-form-select {
  position: relative;
  z-index: 9999;
  padding: 30px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  align-items: center;
  a.node-preview-backlink {
    display: flex;
    padding-bottom: 10px;
    color: $primary;
    width: 30%;
  }
  .js-form-item-view-mode {
    width: 30%;
    label {
      margin-bottom: 10px;
    }
  }
}

fieldset[id^="edit-field-domaine-d-activite-target-id"] {
  margin: 100px 0 8px;
  .fieldset-wrapper {
    .form-checkboxes {
      flex-wrap: wrap;
    }
  }
  @media(max-width: 1400px) {
    margin: 140px 0 8px;
  }
   @media(max-width: 992px) {
    margin: 182px 0 8px;
  }
  @media(max-width: 991px) {
    margin: 234px 0 8px;
  }
  @media(max-width: 594px) {
    margin: 315px 0 8px;
    label {
      padding: 6px 10px !important;
    }
  }
  @media(max-width: 376px) {
    margin: 330px 0 8px;
  }
}
div[class^="form-item-field-domaine-d-activite-target-id"] {
  display: none;
}
div[class^="js-form-item-field-domaine-d-activite-target-id"] {
  display: none;
}


.share-links .btn {
  position: relative;
    /* font-size: 0; */
    width: 48px;
    height: 48px;
    border-radius: 15px !important;
    border: 1px solid #111111;
    display: block;
    background: none;
    color: $black !important;
    font-size: 20px;
    display: flex;
    justify-content: center;
    align-items: center;
    transition: all 450ms ease;
    &:hover {
      background-color: $primary;
      color: $white !important;
      border-color: $primary;
    }
}

//Marge
// .tabs-content + #tab2 + #tab3 {
//   margin-top: 30px;
// }
// .mt-4.clearfix + div.paragraph--type--bloc-decouvrire-aussi {
//   .bloc-decouvrir {
//     margin-top: 30px;
//   }
// }

// .paragraph--type--organigrame + .bloc-partenaire {
//   .liens-utiles {
//     margin-bottom: 0 !important;
//     h2 {
//       margin-bottom: 0;
//     }
//   }
  
// }

.partenaire-slider {
  .swiper-pagination {
    bottom: 0;
  }
}

.body-org .m-organigramme {
  padding-top: 70px;
}
.paragraph--type--bloc-decouvrire-aussi {
  & > .share-buttons {
    margin-bottom: 30px;
  }
}
.marg-org .scroll-wrapper {
  padding-top: 70px !important;
}
//Page cooperation
.wysi-coop {
  padding: 3rem;
  & > .container {
    padding: 0 !important;
    margin: 0 !important;
  }
  .card {
    all: unset;
    padding: 0 !important;
  }
  .wysi-form {
    border: 1px solid #e1e1e1;
    border-radius: 15px;
    padding: 20px;
    @media(min-width: 992px) {
      padding: 30px 100px;
    }
  }
  form {
    box-shadow: none;
    padding: 0;
    .js-form-type-select {
      display: flex;
      align-items: center;
      gap: 50px;
      margin-bottom: 20px;
      
      label[for="edit-tid"] {
        font-family: $quicksand-bold;
        margin-bottom: 0 !important;
      }
       @media(max-width: 480px) {
        flex-direction: column;
        gap: 0px;
        align-items: flex-start;
      }
    }
    .selectric-form-select {
      width: 100%;
    }
    .selectric {
      background: #F2F2F2;
      border-color: #F2F2F2;
    }
    .selectric-items li {
      &:after, &:before {
        display: none;
      }
    }
  }
  form + div {
    p {
      position: relative;
      text-transform: lowercase;
      padding-left: 20px;
      &::first-letter {
        text-transform: uppercase;
      }
      &:before {
        content: "";
        position: absolute;
        background: $primary;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        left: 0;
        top: 8px;
      }
    }
  }
}
.image-block-wrapper {
  .picture {
    position: relative;
    border-radius: 8px;
    border: 1px solid $primary;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 30px 30px 40px;
    margin-bottom: 35px;
    &:after {
      content: "\e903";
      font-family: 'icomoon';
      color: $primary;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 12px;
      position: absolute;
      background: #EBF1FA;
      width: 46px;
      height: 46px;
      border-radius: 50%;
      left: 50%;
      bottom: -23px;
      transform: translateX(-50%);
      transition: transform 450ms ease;
    }
    &.open {
      &:after {
        color: $white;
        background: $primary;
        transform: translateX(-50%) rotateZ(180deg);
      }
    }
  }
  img {
    border-radius: 0 !important;
    padding: 0 !important;
    width: 100%;
    height: 120px;
    object-fit: contain;
  }
  .content {
    &.container {
      border-radius: 8px;
      border: 1px solid $primary;
      margin-left: 0;
    }
    .container-wrapper {
      padding: 20px;
      p {
        text-align: justify;
        hyphens: auto;
      }
      @media(min-width: 768px) {
        padding: 30px;
      }
      @media(min-width: 992px) {
        padding: 40px 80px;
      }
    }
  }
}
.image-block {
  .content {
    width: 100%;
  }
  @media(min-width: 768px) {
    .content {
      width: calc(200% + 15px);
    }
    &:nth-child(2n + 1) {
      .content {
        margin-left: 0;
      }
    }
    &:nth-child(2n + 2) {
      .content {
        margin-left: calc(-100% - 15px);
      }
    }
  }
  @media(min-width: 992px) {
    .content {
      width: calc(300% + 30px);
    }
    &:nth-child(2n + 2) {
      .content {
        margin-left: 0;
      }
    }
    // &:nth-child(2n + 1) {
    //   .content {
    //     margin-left: 0;
    //   }
    // }
    &:nth-child(3n + 2) {
      .content {
        margin-left: calc(-100% - 15px);
      }
    }
    &:nth-child(3n + 3){
      .content {
        margin-left: calc(-200% - 30px);
      }
    }
   
  }
  
}

//contrast form
.overlay {
  position: fixed;
  inset: 0;
  display: none;
  z-index: 1101;
  &::after {
    content: "";
    position: absolute;
    inset: 0;
    background-color: rgba(29, 29, 39, 0.7);
    width: 100vw;
    height: 100vh;
    inset: 0;
  }
  &.open {
    display: block;
  }
}
body.no-scroll {
  overflow-y: scroll;
  position: fixed;
  width: 100%;
  height: 100vh;
  top: 0;
  left: 0;
  #voiceflow-chat{
    z-index: 999 !important;
    position: absolute;
    pointer-events: none;
  }
}

.bloc-identite {
  display: flex;
  align-items: center;
  padding: 30px 0 0;
  gap: 30px;
  .toolbar-horizontal & {
    padding: 50px 0 0;
  }
  .picture {
    flex: 1;
    display: flex;
    background: $primary;
    justify-content: center;
    padding: 30px;
    border-radius: 15px;
    img {
      height: 600px;
      padding: 0;
      border-radius: 0 !important;
    }
  }
  .btn-download {
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 20px;
    flex: 1;
    a {
      width: 100%;
      height: 100px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 20px;
    }
  }
  @media(max-width: 768px) {
    flex-direction: column;
    .picture {
      justify-content: center;
      padding-left: 0;
      padding-right: 0;
      img {
        height: auto;
        width: 50%;
      }
    }
    .btn-download {
     a {
        height: 70px;
        font-size: 18px;
     }

    }
  }
  @media(max-width: 480px) {
    .picture {
      img {
        width: 60%;
      }
    }
    .btn-download {
     a {
        height: 60px;
        font-size: 16px;
     }

    }
  }
}

body.bodyorg {
  .bannerHp__page-interne + div {
    padding: 30px;
  }
}


