<?php

use <PERSON><PERSON>al\Core\Form\FormStateInterface;

/**
 * Implements hook_theme_suggestions_views_view_alter().
 */
function mtl_theme_suggestions_views_view_alter(array &$suggestions, array $variables)
{
  $view = $variables['view'];
  $view_name = $view->id();
  $display_name = $view->current_display;

  $suggestions[] = 'views_view__' . $view_name;

  if ($display_name) {
    $suggestions[] = 'views_view__' . $view_name . '__' . $display_name;
  }
}

/**
 * Implements hook_theme_suggestions_views_view_unformatted_alter().
 */
function mtl_theme_suggestions_views_view_unformatted_alter(array &$suggestions, array $variables)
{
  $view = $variables['view'];
  $view_name = $view->id();
  $display_name = $view->current_display;

  $suggestions[] = 'views_view_unformatted__' . $view_name;

  if ($display_name) {
    $suggestions[] = 'views_view_unformatted__' . $view_name . '__' . $display_name;
  }
}

/**
 * Implements hook_theme_suggestions_views_view_fields_alter().
 */
function mtl_theme_suggestions_views_view_fields_alter(array &$suggestions, array &$variables, $hook)
{
  $view = $variables['view'];
  $view_name = $view->id();
  $display_name = $view->current_display;

  if ($view_name && $display_name) {
    $suggestions[] = $hook . '__' . $view_name . '__' . $display_name;
  }
}

/**
 * Implements hook_theme_suggestions_views_view_field_alter().
 */
function mtl_theme_suggestions_views_view_field_alter(array &$suggestions, array &$variables, $hook)
{
  $view = $variables['view'];
  $view_name = $view->id();
  $display_name = $view->current_display;

  if ($view_name && $display_name) {
    if (isset($variables['field']) && isset($variables['field']->field)) {
      $suggestions[] = $hook . '__' . $view_name . '__' . $display_name . '__' . $variables['field']->field;
    }
  }
}

/**
 * Implements hook_theme_suggestions_views_exposed_form_alter().
 */
function mtl_theme_suggestions_views_exposed_form_alter(array &$suggestions, array $variables)
{
  $form_id = $variables['form']['#id'];

  $suggestions[] = 'views_exposed_form__' . str_replace(
    ['views-exposed-form-', '-'],
    ['', '_'],
    $form_id
  );

  if (preg_match('/views-exposed-form-(?<viewname>[^-]+)-(?<displayname>.+)/', $form_id, $matches)) {
    $suggestions[] = 'views_exposed_form__' . $matches['viewname'] . '__' . $matches['displayname'];
  }
}

function mtl_preprocess_views_view(&$variables)
{
  if ($variables['more']) {
    $renderer = \Drupal::service('renderer');
    $more = $renderer->render($variables['more']);
    preg_match('/href="([^"]+)"/', $more, $matches);
    $variables['more_url'] = $matches[1] ?? '';
  }

  // Masquer complètement les blocs de vue qui n'ont pas de contenu
  // MAIS PAS les pages complètes de vue
  $view = $variables['view'];
  $display_id = $view->current_display;

  // Exclure les displays de type 'page' pour ne pas impacter les pages complètes
  if ($display_id !== 'page' && !in_array($display_id, ['page_1', 'page_2', 'page_3'])) {
    $has_content = FALSE;

    // Vérifier s'il y a des résultats dans la vue
    if (!empty($variables['rows'])) {
      $has_content = TRUE;
    }

    // Si la vue n'a pas de contenu, ajouter une classe CSS pour la masquer
    if (!$has_content) {
      $variables['attributes']['class'][] = 'view-empty-hidden';
      $variables['attributes']['style'] = 'display: none !important;';
    }
  }
}

function mtl_form_alter(&$form, $form_state, $form_id)
{
  if ($form_id == 'views_exposed_form') {
    foreach ($form as &$field) {
      if (is_array($field) && isset($field['#options']['All'])) {
        if (\Drupal::languageManager()->getCurrentLanguage()->getId() === 'ar') {
          $field['#options']['All'] = 'عرض الكل';
        }
        elseif(\Drupal::languageManager()->getCurrentLanguage()->getId() === 'en') 
        {
          $field['#options']['All'] = 'Show all';
        }
        else {
          $field['#options']['All'] = 'Tout afficher';
        }
      }
    }
  }
  if ($form_id == 'views_exposed_form' && isset($form['#id'])) {
    if (isset($form['actions']['submit'])) {
      $form['actions']['submit']['#prefix'] = '<div class="input-wrapper">';
      $form['actions']['submit']['#suffix'] = '</div>';
    }
  }

  // Add color picker for field_color in node forms
  if (strpos($form_id, 'node_') === 0 && strpos($form_id, '_form') !== false) {
    if (isset($form['field_color'])) {
      $form['field_color']['widget'][0]['value']['#type'] = 'color';
      $form['field_color']['widget'][0]['value']['#attributes']['class'][] = 'color-picker-field';
    }
  }
}

function mtl_preprocess_page_title(&$variables)
{
  $routeMatch = \Drupal::routeMatch();

  if ($node = $routeMatch->getParameter('node')) {
    // dd($node);
    $variables['current_entity'] = $node;
  }

  if ($term = $routeMatch->getParameter('taxonomy_term')) {
    $variables['current_entity'] = $term;
  }
}

function mtl_preprocess(&$variables, $hook)
{
  $langId = \Drupal::languageManager()->getCurrentLanguage()->getId();
  $variables['lang_id'] = $langId;
}

function mtl_preprocess_page(&$variables) {
  if (\Drupal::routeMatch()->getRouteName() == 'entity.taxonomy_term.canonical') {
    $term = \Drupal::routeMatch()->getParameter('taxonomy_term');
    if ($term instanceof \Drupal\taxonomy\Entity\Term) {
      $variables['taxonomy_term'] = $term;
    }
  }
}

/**
 * Validates and prepares image data for a node.
 */
function _mtl_validate_node_image($node) {
  $validation = [
    'is_valid' => false,
    'uri' => null,
    'alt' => null,
    'error' => null
  ];

  try {
    // Check field_image_media first
    if ($node->hasField('field_image_media') && !$node->get('field_image_media')->isEmpty()) {
      $media = $node->get('field_image_media')->entity;
      if ($media && $media->hasField('field_media_image') && !$media->get('field_media_image')->isEmpty()) {
        $file = $media->get('field_media_image')->entity;
        if ($file) {
          $uri = $file->getFileUri();
          $file_system = \Drupal::service('file_system');
          $real_path = $file_system->realpath($uri);
          
          if ($real_path && file_exists($real_path) && is_readable($real_path)) {
            $validation['is_valid'] = true;
            $validation['uri'] = $uri;
            $validation['alt'] = $media->get('field_media_image')->alt ?? 'Image';
            return $validation;
          }
        }
      }
    }

    // Check field_image as fallback
    if ($node->hasField('field_image') && !$node->get('field_image')->isEmpty()) {
      $file = $node->get('field_image')->entity;
      if ($file) {
        $uri = $file->getFileUri();
        $file_system = \Drupal::service('file_system');
        $real_path = $file_system->realpath($uri);
        
        if ($real_path && file_exists($real_path) && is_readable($real_path)) {
          $validation['is_valid'] = true;
          $validation['uri'] = $uri;
          $validation['alt'] = $node->get('field_image')->alt ?? 'Image';
          return $validation;
        }
      }
    }

    $validation['error'] = 'No valid image found';
  } catch (\Exception $e) {
    $validation['error'] = $e->getMessage();
  }

  return $validation;
}

/**
 * Implements hook_preprocess_views_view_fields().
 */
function mtl_preprocess_views_view_fields(&$variables) {
  $view = $variables['view'];
  
  // Only process for actualite view
  if ($view->id() === 'actualite') {
    $row = $variables['row'];
    
    // Get the node entity
    if (isset($row->_entity) && $row->_entity->getEntityTypeId() === 'node') {
      $node = $row->_entity;
      
      // Validate image
      $variables['image_validation'] = _mtl_validate_node_image($node);
      
      // Set default image path - use global base_path
      global $base_path;
      $variables['default_image_path'] = $base_path . 'themes/custom/mtl/assets/images/default-actualite.jpg';
    }
  }
}

/**
 * Implements hook_preprocess_node().
 */
function mtl_preprocess_node(&$variables) {
  $node = $variables['node'];
  
  // Only process for actualite content type
  if ($node->getType() === 'actualite') {
    // Validate image using the same function
    $variables['image_validation'] = _mtl_validate_node_image($node);
    
    // Set default image path - use global base_path
    global $base_path;
    $variables['default_image_path'] = $base_path . 'themes/custom/mtl/assets/images/default-actualite.jpg';
  }
}

/**
 * Implements template_preprocess_views_exposed_form().
 */
function mtl_preprocess_views_exposed_form(&$variables) {
  if ($variables['form']['#id'] == 'views-exposed-form-pays-block-1') {
    foreach ($variables['form'] as &$field) {
      if (is_array($field) && isset($field['#options']['All'])) {
        unset($field['#options']['All']);
      }
    }
  }
}

/**
 * Implements hook_preprocess_block().
 * Masque complètement les blocs de vue qui n'ont pas de contenu.
 * N'affecte PAS les pages complètes de vue.
 */
function mtl_preprocess_block(&$variables) {
  // Vérifier si c'est un bloc de vue (pas une page complète)
  if (isset($variables['plugin_id']) && strpos($variables['plugin_id'], 'views_block:') === 0) {
    // Récupérer le contenu du bloc
    $content = $variables['content'];

    // Vérifier si le bloc a du contenu
    $has_content = FALSE;

    if (isset($content['#view']) && is_object($content['#view'])) {
      $view = $content['#view'];
      $display_id = $view->current_display;

      // S'assurer que ce n'est pas un display de page
      if ($display_id !== 'page' && !in_array($display_id, ['page_1', 'page_2', 'page_3']) && strpos($display_id, 'page') !== 0) {
        // Vérifier s'il y a des résultats
        if (!empty($view->result)) {
          $has_content = TRUE;
        }

        // Si le bloc n'a pas de contenu, le masquer complètement
        if (!$has_content) {
          $variables['attributes']['class'][] = 'block-empty-hidden';
          $variables['attributes']['style'] = 'display: none !important;';

          // Optionnel : vider complètement le contenu pour économiser les ressources
          $variables['content'] = [];
        }
      }
    }
  }
}

