{#
/**
 * Template de debug pour comprendre pourquoi les messages empty ne s'affichent pas
 * Renommez ce fichier en views-view.html.twig temporairement pour debug
 */
#}
{%
  set classes = [
    dom_id ? 'js-view-dom-id-' ~ dom_id,
  ]
%}

{# DEBUG INFO #}
<div style="background: #f0f0f0; padding: 10px; margin: 10px 0; border: 1px solid #ccc;">
  <strong>DEBUG VIEW INFO:</strong><br>
  View ID: {{ view.id() }}<br>
  Display ID: {{ view.current_display }}<br>
  Has rows: {{ rows ? 'YES' : 'NO' }}<br>
  Has empty: {{ empty ? 'YES' : 'NO' }}<br>
  Empty content: {{ empty|raw }}<br>
</div>

<div{{ attributes.addClass(classes) }}>
  {{ title_prefix }}
  {{ title }}
  {{ title_suffix }}

  {% if header %}
    <header>
      {{ header }}
    </header>
  {% endif %}

  {{ exposed }}
  {{ attachment_before }}

  {% if rows -%}
    <div style="background: #e8f5e8; padding: 10px; margin: 5px 0;">
      <strong>AFFICHAGE DES ROWS:</strong>
    </div>
    {{ rows }}
  {% elseif empty -%}
    <div style="background: #ffe8e8; padding: 10px; margin: 5px 0;">
      <strong>AFFICHAGE DU MESSAGE EMPTY:</strong>
    </div>
    {{ empty }}
  {% else %}
    <div style="background: #fff8e8; padding: 10px; margin: 5px 0;">
      <strong>AUCUN CONTENU - NI ROWS NI EMPTY</strong>
    </div>
  {% endif %}
  
  {{ pager }}

  {{ attachment_after }}
  {{ more }}

  {% if footer %}
    <footer>
      {{ footer }}
    </footer>
  {% endif %}

  {{ feed_icons }}
</div>
