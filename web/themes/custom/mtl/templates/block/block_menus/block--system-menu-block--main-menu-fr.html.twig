{#
/**
 * @file
 * Default theme implementation for a menu block.
 *
 * Available variables:
 * - plugin_id: The ID of the block implementation.
 * - label: The configured label of the block if visible.
 * - configuration: A list of the block's configuration values.
 *   - label: The configured label for the block.
 *   - label_display: The display settings for the label.
 *   - provider: The module or other provider that provided this block plugin.
 *   - Block plugin specific settings will also be stored here.
 * - in_preview: Whether the plugin is being rendered in preview mode.
 * - content: The content of this block.
 * - attributes: HTML attributes for the containing element.
 *   - id: A valid HTML ID and guaranteed unique.
 * - title_attributes: HTML attributes for the title element.
 * - content_attributes: HTML attributes for the content element.
 * - title_prefix: Additional output populated by modules, intended to be
 *   displayed in front of the main title tag that appears in the template.
 * - title_suffix: Additional output populated by modules, intended to be
 *   displayed after the main title tag that appears in the template.
 *
 * Headings should be used on navigation menus that consistently appear on
 * multiple pages. When this menu block's label is configured to not be
 * displayed, it is automatically made invisible using the 'visually-hidden' CSS
 * class, which still keeps it visible for screen-readers and assistive
 * technology. Headings allow screen-reader and keyboard only users to navigate
 * to or skip the links.
 * See http://juicystudio.com/article/screen-readers-display-none.php and
 * http://www.w3.org/TR/WCAG-TECHS/H42.html for more information.
 *
 * @ingroup themeable
 */
#}
{# FRENCH logo #}
{% set heading_id = attributes.id ~ ('-menu'|clean_id) %}
{% set logo_fr_alt = config_pages_field('mtl', 'field_logo_fr')['#items'].entity.image.alt %}
{% set logo_fr_uri = config_pages_field('mtl', 'field_logo_fr')['#items'].entity.uri.value %}
{% set logo_fr_sticky_uri = config_pages_field('mtl', 'field_logo_fr_sticky')['#items'].entity.uri.value %}
{% set logo_fr_sticky_alt = config_pages_field('mtl', 'field_logo_fr_sticky')['#items'].entity.image.alt %}
{# ARABIC logo #}
{% set logo_ar_alt = config_pages_field('mtl', 'field_logo_ar')['#items'].entity.image.alt %}
{% set logo_ar_uri = config_pages_field('mtl', 'field_logo_ar')['#items'].entity.uri.value %}
{% set logo_ar_sticky_uri = config_pages_field('mtl', 'field_logo_ar_sticky')['#items'].entity.uri.value %}
{% set logo_ar_sticky_alt = config_pages_field('mtl', 'field_logo_ar_sticky')['#items'].entity.image.alt %}
{# ENGLISH logo #}
{% set logo_en_alt = config_pages_field('mtl', 'field_logo_en')['#items'].entity.image.alt %}
{% set logo_en_uri = config_pages_field('mtl', 'field_logo_en')['#items'].entity.uri.value %}
{% set logo_en_sticky_uri = config_pages_field('mtl', 'field_logo_en_sticky')['#items'].entity.uri.value %}
{% set logo_en_sticky_alt = config_pages_field('mtl', 'field_logo_en_sticky')['#items'].entity.image.alt %}

{# Check if current URL contains language patterns - Drupal way #}
{% set current_route = path('<current>') %}



<div class="header__bottom">
<div class="overlay"></div>
  <div class="logo">
    {% if '/fr' in current_route %}
      <a href="{{ path('<front>') }}">
      <img src="{{ file_url(logo_fr_uri) }}" class="logo-header" alt="{{ logo_fr_alt }}" fetchpriority="high" width="300" height="78" loading="lazy" />
      <img src="{{ file_url(logo_fr_sticky_uri) }}" class="logo-header-sticky" alt="{{ logo_fr_sticky_alt }}" width="270" height="70" loading="lazy" />
    </a>
    {% elseif '/ar' in current_route or '/AMZ' in current_route %}
      <a href="{{ path('<front>') }}">
      <img src="{{ file_url(logo_ar_uri) }}" class="logo-header" alt="{{ logo_ar_alt }}" fetchpriority="high" width="300" height="78" loading="lazy" />
      <img src="{{ file_url(logo_ar_sticky_uri) }}" class="logo-header-sticky" alt="{{ logo_ar_sticky_alt }}" width="270" height="70" loading="lazy" />
    </a>
    {% elseif '/en' in current_route %}
      <a href="{{ path('<front>') }}">
        <img src="{{ file_url(logo_en_uri) }}" class="logo-header" alt="{{ logo_en_alt }}" fetchpriority="high" width="300" height="78" loading="lazy" />
      <img src="{{ file_url(logo_en_sticky_uri) }}" class="logo-header-sticky" alt="{{ logo_en_sticky_alt }}" width="270" height="70" loading="lazy" />
    </a>
    {% endif %}
  </div>
  <div class="wrapper-menu-form">
    <div class="wrapper">
      <div class="header__bottom--mainMenu">
        <input type="checkbox" id="nav-toggle" class="nav-toggle" />
        <label for="nav-toggle" class="nav-toggle-label"><span></span></label>
        <nav role="navigation" aria-labelledby="{{ heading_id }}" {{ attributes|without('role', 'aria-labelledby') }}>
          {# Menu. #}
          {% block content %}
            {{ content }}
          {% endblock %}
        </nav>
      </div>
      <div class="search-icon"></div>
    </div>
  </div>

  <div class="header__bottom--overlay">
    <form action="/fr/recherche" method="get" id="views-exposed-form-recherche-searchapi-page-1" accept-charset="UTF-8">
      <div class="js-form-item form-item js-form-type-textfield form-item-search-api-fulltext js-form-item-search-api-fulltext">
        <label for="edit-search-api-fulltext--2">Recherche en texte intégral</label>
        <input data-drupal-selector="edit-search-api-fulltext" type="text" id="edit-search-api-fulltext--2" name="search_api_fulltext" value="" size="30" maxlength="128" class="form-text" placeholder={{'Recherche en texte intégral...'|t}} />
      </div>
      <div data-drupal-selector="edit-actions" class="form-actions js-form-wrapper form-wrapper" id="edit-actions--2">
        <input data-drupal-selector="edit-submit-recherche-searchapi-2" type="submit" id="edit-submit-recherche-searchapi--2" value="Search" class="button js-form-submit form-submit" />
      </div>
    </form>
  </div>
</div>
